<template>
  <common-dialog
    title="签到奖励配置"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    :show-footer="true"
    @closed="handleClosed"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="dialog-main">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-position="top">
        <el-form-item label="签到规则配置" prop="ruleContent">
          <wang-editor
            ref="ruleContent"
            v-model="form.ruleContent"
            :content.sync="form.ruleContent"
            :height="300"
          />
        </el-form-item>
        <el-form-item label="签到提醒文案" prop="remindText">
          <el-input
            v-model="form.remindText"
            type="textarea"
            :maxlength="20"
            show-word-limit
            placeholder="请输入签到提醒文案"
          />
        </el-form-item>
        <el-form-item label="签到奖励配置（铁粉用户享奖励翻倍权益）">
          <el-table
            :data="form.rewardList"
            border
            size="small"
            class="table-container"
          >
            <el-table-column label="连续签到" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.signDays }}天</span>
              </template>
            </el-table-column>
            <el-table-column label="奖励智米数" align="center">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'rewardList.' + scope.$index + '.rewardZhimi'"
                  :rules="rules.rewardZhimi"
                  :show-message="false"
                  class="table-form-item"
                >
                  <el-input-number
                    v-model="scope.row.rewardZhimi"
                    :min="0"
                    :max="9999999"
                    size="mini"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
          <div
            v-if="rewardListError"
            class="el-form-item__error"
            style="position: relative"
          >
            {{ rewardListError }}
          </div>
        </el-form-item>
        <el-form-item label="变更记录">
          <el-table
            :data="changeRecords"
            border
            size="small"
            class="table-container"
          >
            <el-table-column prop="editor" label="编辑人" align="center" />
            <el-table-column prop="content" label="编辑内容" align="center">
              <template slot-scope="scope">
                <div v-html="scope.row.content" />
              </template>
            </el-table-column>

            <el-table-column
              prop="editTime"
              label="编辑时间"
              width="180"
              align="center"
            />
          </el-table>
          <div class="yz-table-pagination">
            <pagination
              v-show="page.total > 0"
              size="mini"
              :total="page.total"
              :page.sync="page.current"
              :limit.sync="page.size"
              @pagination="fetchChangeRecords"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import WangEditor from '@/components/WangEditor';
import Pagination from '@/components/Pagination';
export default {
  name: 'SignRuleDialog',

  components: {
    WangEditor,
    Pagination
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      form: {
        ruleContent: '',
        remindText: '',
        rewardList: [],
        originalRewardList: null
      },
      rules: {
        ruleContent: [
          { required: true, message: '请输入签到规则内容', trigger: 'blur' }
        ],
        remindText: [
          { required: true, message: '请输入签到提醒文案', trigger: 'blur' },
          { max: 20, message: '提醒文案不能超过20个字符', trigger: 'blur' }
        ],
        rewardZhimi: [
          { required: true, message: '请填写奖励智米数', trigger: 'blur' }
        ]
      },
      rewardListError: null,
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      changeRecords: []
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData();
      }
    },
    'form.rewardList': {
      deep: true,
      handler(newVal) {
        if (this.rewardListError) {
          const isStillInvalid = newVal.some(
            (item) =>
              item.rewardZhimi === undefined || item.rewardZhimi === null
          );
          if (!isStillInvalid) {
            this.rewardListError = null;
          }
        }
      }
    }
  },

  methods: {
    // 初始化数据
    async initData() {
      try {
        // 并发调用两个接口
        const [configResult, rewardResult] = await Promise.all([
          this.$http.get('/operate/sign/config/detail', { json: true }),
          this.$http.get('/operate/sign/reward/all', { json: true })
        ]);

        if (configResult.code === '00' && rewardResult.code === '00') {
          // 处理签到配置数据
          const configData = configResult.body;
          // 处理奖励数据
          const rewardList = rewardResult?.body;

          this.form = {
            configId: configData?.id,
            ruleContent: configData.signRuleDesc || '',
            remindText: configData.signTip || '',
            originalRewardList: JSON.parse(JSON.stringify(rewardList ?? [])),
            rewardList:
              rewardList.length > 0
                ? rewardList
                : Array.from({ length: 7 }, (_, i) => ({
                  signDays: i + 1,
                  rewardZhimi: undefined
                }))
          };
          this.$nextTick(() => {
            this.$refs.ruleContent.setContent(this.form.ruleContent);
          });
          // 获取变更记录
          await this.fetchChangeRecords();
        } else {
          throw new Error('获取数据失败');
        }
      } catch (error) {
        console.error(error);
        this.$message.error('获取数据失败');
        this.$refs.ruleContent.setContent(this.form.ruleContent);
        // 即使出错也尝试获取变更记录
        await this.fetchChangeRecords();
      }
    },

    // 获取变更记录
    async fetchChangeRecords() {
      try {
        const params = {
          operateBusinessType: 3, // 3 签到
          pageNum: (this.page.current - 1) * this.page.size,
          pageSize: this.page.size
        };
        this.$http
          .post('/operate/common/list', params, { json: true })
          .then((res) => {
            const { fail, body } = res;
            if (!fail) {
              this.changeRecords = body?.data?.map((item) => ({
                editor: item.operateUser,
                content: item.operateContent,
                editTime: item.operateTime
              }));
              this.page.total = body.recordsTotal;
            } else {
              this.changeRecords = [];
              this.page.total = 0;
            }
          });
      } catch (error) {
        console.error('获取变更记录失败:', error);
        this.changeRecords = [];
        this.page.total = 0;
      }
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit('update:visible', false);
    },

    handleConfirm() {
      this.rewardListError = null;
      this.$refs.ruleForm.validate((valid, invalidFields) => {
        if (valid) {
          this.handleSaveSignRule(this.form);
        } else {
          if (
            invalidFields &&
            Object.keys(invalidFields).some((key) =>
              key.startsWith('rewardList.')
            )
          ) {
            this.rewardListError = '请填写所有天数的奖励智米数';
          }
        }
      });
    },

    handleClosed() {
      this.$refs.ruleForm.resetFields();
      this.form = {
        ruleContent: '',
        remindText: '',
        rewardList: [],
        originalRewardList: null
      };
      this.changeRecords = [];
      this.page.current = 1;
      this.page.total = 0;
      this.$emit('update:visible', false);
    },

    // 保存签到规则
    async handleSaveSignRule(data) {
      try {
        // 准备配置数据
        const configParams = {
          id: data?.configId,
          signRuleDesc: data.ruleContent,
          signTip: data.remindText
        };

        // 准备奖励数据
        const rewardParams = {
          rewardList: data.rewardList,
          operateContent: ''
        };

        // 比较奖励数据是否有变化，写入操作日志
        const originalRewardList = this.form.originalRewardList;
        if (originalRewardList.length) {
          rewardParams.operateContent = originalRewardList
            .map((item, index) => {
              const originalPoints = item.rewardZhimi;
              const newPoints = data.rewardList[index].rewardZhimi;
              if (originalPoints !== newPoints) {
                return `连续签到${item.signDays}天: “奖励${originalPoints}智米数”改为“奖励${newPoints}智米数”`;
              }
              return '';
            })
            .filter(Boolean)
            .join('<br/>');
        }
        const rewardUrl = this.form.originalRewardList.length
          ? '/operate/sign/reward/batchUpdate'
          : '/operate/sign/reward/batchAdd';
        // 并发调用两个接口
        const [configResult, rewardResult] = await Promise.all([
          this.$http.post('/operate/sign/config/edit', configParams, {
            json: true
          }),
          // 如果没有非新增情况下，奖励数据没有变化，则不调用接口
          originalRewardList.length && !rewardParams.operateContent
            ? Promise.resolve({ code: '00' })
            : this.$http.post(rewardUrl, rewardParams, {
              json: true
            })
        ]);

        if (configResult.code === '00' && rewardResult.code === '00') {
          this.$message.success('保存成功');
          this.handleClosed();
        } else {
          this.$message.error(
            configResult.body.message || rewardResult.body.message || '保存失败'
          );
        }
      } catch (error) {
        console.error(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;
}

.yz-table-pagination {
  text-align: right;
}

.table-form-item {
  margin-bottom: 0;
}
</style>
