import Vue from 'vue';

import 'normalize.css/normalize.css'; // A modern alternative to CSS resets

import ElementUI from 'element-ui';
import { insertDictAndPcd } from '@/utils';
import { request } from '@/api';
import axios from '@/utils/axios';
import store from './store';
import router from './router';
import localDict from './dict';
import testData from './testdata';
// 安装全局组件
import './components/install.js';
// 安装全局过滤器
import './filters/install.js';
// 注册全局自定义指令
import '@/directives/install.js';

import 'element-ui/lib/theme-chalk/index.css';
// import locale from 'element-ui/lib/locale/lang/en'; // lang i18n

import '@/styles/index.scss'; // global css
import '@/icons'; // icon
import '@/permission'; // permission control

import App from './App';

// 初始化字典数据
insertDictAndPcd().then(() => {
  // window.parent.dictJson = JSON.parse(JSON.stringify(dictJson)); // 把字典挂载在全局
  window.dictJson = JSON.parse(JSON.stringify(dictJson)); // 把字典挂载在全局
});
// 挂在全局属性
Vue.prototype.$post = request;
Vue.prototype.$http = axios;
// Vue.prototype.$dictJson = window.parent.dictJson;
Vue.prototype.$dictJson = window.dictJson;
Vue.prototype.$pcdJson = pcdJson;
Vue.prototype.$pcdJdJson = pcdJdJson;
Vue.prototype.$localDict = localDict;// 本地词典
Vue.prototype.$testData = testData;// 本地词典
// set ElementUI lang to EN , { locale }
Vue.use(ElementUI);
// 如果想要中文版 element-ui，按如下方式声明
// Vue.use(ElementUI)

Vue.config.productionTip = false;

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
});
