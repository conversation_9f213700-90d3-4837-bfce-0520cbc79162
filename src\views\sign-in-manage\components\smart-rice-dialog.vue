<template>
  <common-dialog
    title="赚取智米配置"
    :visible.sync="visible"
    width="1000px"
    :close-on-click-modal="false"
    append-to-body
    @closed="handleClosed"
  >
    <div class="dialog-main">
      <el-form :model="searchForm" class="yz-search-form" size="mini" label-width="80px">
        <el-form-item label="主标题">
          <el-input v-model="searchForm.title" placeholder="请输入主标题" />
        </el-form-item>
        <el-form-item label="启用状态">
          <el-select v-model="searchForm.enabled" placeholder="请选择">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="handleReset" />
        </div>
      </el-form>

      <div class="yz-operation-box">
        <el-button type="primary" size="mini" @click="handleAdd">新增</el-button>
      </div>

      <el-table v-loading="loading" :data="tableData" border size="small" class="table-container">
        <el-table-column prop="title" label="主标题" align="center" />
        <el-table-column prop="subtitle" label="副标题" align="center" />
        <el-table-column prop="jumpUrl" label="跳转地址" show-overflow-tooltip align="center" />
        <el-table-column label="启用状态" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.enabled ? '启用' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="权重" width="100" align="center" />
        <el-table-column label="操作" width="100" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="yz-table-pagination">
        <pagination
          :total="page.total"
          :page.sync="page.current"
          :limit.sync="page.size"
          @pagination="handlePagination"
        />
      </div>
    </div>

    <!-- 编辑抽屉 -->
    <el-drawer :visible.sync="drawerVisible" :title="formData.id ? '编辑' : '新增'" size="500px" append-to-body>
      <el-form ref="form" :model="formData" :rules="rules" label-width="100px" class="drawer-form" size="mini">
        <el-form-item label="图标" prop="icon">
          <el-upload
            action="/file/uploadFileToTemp.do"
            list-type="picture-card"
            accept="image/png,image/jpeg,image/gif"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-success="handleIconSuccess"
            :before-upload="(file)=> fieldData.fileName = file.name"
            :data="fieldData"
            name="fileData"
            fileData
            :limit="1"
            :class="{ 'hide-upload': formData.icon }"
            :file-list="fileList"
          >
            <i class="el-icon-plus" />
          </el-upload>
          <common-dialog :visible.sync="showViewer" append-to-body width="500px">
            <img v-if="formData.icon" width="100%" :src="formData.icon">
          </common-dialog>
        </el-form-item>
        <el-form-item label="主标题" prop="title">
          <el-input v-model="formData.title" />
        </el-form-item>
        <el-form-item label="副标题" prop="subtitle">
          <el-input v-model="formData.subtitle" />
        </el-form-item>
        <el-form-item label="跳转地址" prop="jumpUrl">
          <el-input v-model="formData.jumpUrl" />
        </el-form-item>
        <el-form-item label="权重值" prop="weight">
          <el-input-number v-model="formData.weight" :min="0" :max="9999" controls-position="right" />
        </el-form-item>
        <el-form-item label="启用状态" prop="enabled">
          <el-radio-group v-model="formData.enabled">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="drawer-footer">
        <el-button size="mini" @click="drawerVisible = false">取消</el-button>
        <el-button type="primary" size="mini" @click="handleSave">确定</el-button>
      </div>
    </el-drawer>

  </common-dialog>
</template>

<script>
import Pagination from '@/components/Pagination';
import { ossUri } from '@/config/request';

const InitForm = {
  id: null,
  icon: '',
  title: '',
  subtitle: '',
  jumpUrl: '',
  weight: 0,
  enabled: 1
};
export default {
  name: 'SmartRiceDialog',
  components: { Pagination },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      loading: false,
      searchForm: {
        title: '',
        enabled: null
      },
      tableData: [],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      drawerVisible: false,
      formData: { ...InitForm },
      fieldData: {
        fileName: ''
      },
      fileList: [],
      rules: {
        icon: [
          { required: true, message: '请上传图标', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入主标题', trigger: 'blur' }
        ],
        subtitle: [
          { required: true, message: '请输入副标题', trigger: 'blur' }
        ],
        jumpUrl: [
          { required: true, message: '请输入跳转地址', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入权重值', trigger: 'blur' }
        ]
      },
      ossUri: ossUri,
      showViewer: false
    };
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.fetchData();
        }
      }
    }
  },

  methods: {
    // 获取数据
    async fetchData() {
      try {
        this.loading = true;
        const params = {
          pageNum: (this.page.current - 1) * this.page.size,
          pageSize: this.page.size,
          title: this.searchForm.title || '',
          ifAllow: this.searchForm.enabled
        };

        const result = await this.$http.post(
          '/operate/sign/earn/list',
          params,
          { json: true }
        );

        this.tableData = result.body.data.map(item => ({
          id: item.id,
          icon: item.icon,
          title: item.title,
          subtitle: item.subTitle,
          jumpUrl: item.route,
          enabled: item.ifAllow,
          weight: parseInt(item.sort)
        }));
        this.page.total = result.body.recordsTotal;
      } catch (error) {
        console.error(error);
        this.$message.error('获取数据失败');
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.page.current = 1;
      this.fetchData();
    },

    // 重置搜索表单
    handleReset() {
      this.searchForm = {
        title: '',
        enabled: null
      };
      this.page.current = 1;
      this.fetchData();
    },

    // 分页变化
    handlePagination(val) {
      this.page.current = val.page;
      this.page.size = val.limit;
      this.fetchData();
    },
    handleRemove() {
      this.formData.icon = null;
    },
    handlePreview() {
      this.showViewer = true;
    },

    handleIconSuccess(response) {
      if (response.code === '00') {
        this.formData.icon = this.fieldData.fileName;
      } else {
        this.$message.error(response.msg || '上传失败');
      }
    },

    handleEdit(row) {
      this.formData = { ...row };
      this.fileList = row.icon ? [{ url: row.icon }] : [];
      this.drawerVisible = true;
    },

    handleAdd() {
      this.formData = { ...InitForm };
      this.fileList = [];
      this.fieldData = {
        fileName: ''
      };
      this.drawerVisible = true;
    },

    async handleSave() {
      try {
        await this.$refs.form.validate();

        const params = {
          title: this.formData.title,
          subTitle: this.formData.subtitle,
          icon: this.formData.icon,
          route: this.formData.jumpUrl,
          sort: this.formData.weight.toString(),
          ifAllow: this.formData.enabled
        };

        let url;
        if (this.formData.id) {
          // 编辑现有数据
          url = '/operate/sign/earn/edit';
          params.id = this.formData.id;
        } else {
          // 新增数据
          url = '/operate/sign/earn/add';
        }

        const res = await this.$http.post(url, params, { json: true });

        if (res.code === '00') {
          this.$message.success(this.formData.id ? '修改成功' : '新增成功');
          this.drawerVisible = false;
          this.fetchData();
        } else {
          this.$message.error(res.msg || '保存失败');
        }
      } catch (error) {
        console.error(error);
      }
    },

    handleCancel() {
      this.$emit('update:visible', false);
    },

    handleClosed() {
      this.$emit('update:visible', false);
      this.searchForm = {
        title: '',
        enabled: null
      };
      this.tableData = [];
      this.page = {
        current: 1,
        size: 10,
        total: 0
      };
      this.drawerVisible = false;
      this.formData = { ...InitForm };
      this.fileList = [];
      this.fieldData = {
        fileName: ''
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-search-form {
  margin: 0 20px 20px;
}

.yz-operation-box {
  margin-bottom: 10px;
  text-align: right;
}

.table-container {
  margin-top: 10px;
}

.yz-table-pagination {
  margin-top: 20px;
  text-align: right;
}

.drawer-form {
  padding: 20px;
}

.el-input-number {
  width: 100%;

  ::v-deep .el-input__inner {
    text-align: left;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  padding: 20px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #e4e7ed;
}

.hide-upload ::v-deep .el-upload {
  display: none;
}
</style>
