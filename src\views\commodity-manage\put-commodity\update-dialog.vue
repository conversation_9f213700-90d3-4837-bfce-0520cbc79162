<template>
  <div>
    <common-dialog
      v-loading="loading"
      :show-footer="true"
      width="80%"
      :title="title"
      :visible.sync='visible'
      @open="init"
      @confirm="submitBtn"
      @close='close'
    >
      <div class="padding-20">
        <el-form
          ref="form"
          class="form"
          size='mini'
          :model="form"
          label-width="120px"
          :rules="rules"
        >
          <el-form-item label='类型' prop='goodsShelfType'>
            <el-select
              v-model="form.goodsShelfType"
              placeholder="请选择"
              :disabled="isEdit"
              @change="clearCommodity"
            >
              <el-option
                v-for="item in $localDict['ShelfType']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label='名称' prop='goodsShelfName'>
            <el-input v-model="form.goodsShelfName" maxlength="100" placeholder="请输入" />
          </el-form-item>

          <el-form-item label='关联商品' prop='commodity'>
            <el-button type="primary" size="small" :disabled="isEdit" @click="sgVisible=true">关联</el-button>
            <div v-width="500" class="table">
              <el-table
                v-if="tableData.length>0"
                ref="notSelectedTable"
                border
                size="small"
                max-height="250px"
                :data="tableData"
                style="width: 100%"
                header-cell-class-name='table-cell-header'
              >
                <el-table-column label="排序" align="center" prop="sort" width="160">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.sort" :controls="false" :min="0" :precision="0" size="small" :disabled="isEdit" />
                  </template>
                </el-table-column>
                <el-table-column label="商品" align="center" prop="goodsName" width="200" />
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <div class="yz-button-area">
                      <el-button type="text" :disabled="isEdit" @click="deleteGoods(scope.row,scope.$index)">删除</el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>

          <el-form-item v-if="goodsId?false:true" label='库存' prop='stock'>
            <el-input-number
              v-model="form.stock"
              :min="0"
              :max="1000000000"
              :precision="0"
              class="yz-input-number"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label='会员价' prop='vipPrice'>
            <el-input-number
              v-model="form.vipPrice"
              :min="0"
              :max="1000000000"
              :precision="2"
              class="yz-input-number yuan"
              :controls="false"
              @blur="computeVipPrice"
            />
          </el-form-item>

          <el-form-item label='活动价' prop='actPrice'>
            <el-input-number
              v-model="form.actPrice"
              :min="0"
              :max="1000000000"
              :precision="2"
              class="yz-input-number yuan"
              :controls="false"
              @blur="computeActPrice"
            />
          </el-form-item>

          <el-form-item label='市场价' prop='marketPrice'>
            <el-input-number
              v-model="form.marketPrice"
              :min="0"
              :max="1000000000"
              :precision="2"
              class="yz-input-number yuan"
              :controls="false"
            />
          </el-form-item>

          <!-- <el-form-item label='员工价' prop='empPrice'>
            <el-input-number
              v-model="form.empPrice"
              :min="0"
              :max="1000000000"
              :precision="2"
              class="yz-input-number yuan"
              :controls="false"
            />
          </el-form-item> -->

          <el-form-item label='购买基数' prop='purchaseBaseNumber'>
            <el-input-number
              v-model="form.purchaseBaseNumber"
              class="yz-input-number"
              :min="0"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label='抵扣方式' prop='purchaseRule'>
            <el-checkbox-group v-model="form.purchaseRule">
              <el-checkbox label="zhimi">智米</el-checkbox>
              <el-checkbox label="retention">滞留金</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label='是否支持退款' prop='refundSupport'>
            <el-radio-group v-model="form.refundSupport" @change="handleValidateRule">
              <el-radio :label="1">支持</el-radio>
              <el-radio :label="2">不支持</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label='可退款项目' prop='refundRule'>
            <el-checkbox-group v-model="form.refundRule">
              <el-checkbox label="cash">现金</el-checkbox>
              <el-checkbox label="zhimi">智米</el-checkbox>
              <el-checkbox label="retention">滞留金</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label='是否支持分期' prop='installmentSupport'>
            <el-radio-group v-model="form.installmentSupport" @change="instsupportRule">
              <el-radio :label="0">不支持</el-radio>
              <el-radio :label="1">支持</el-radio>
            </el-radio-group>
            <template v-if="form.installmentSupport === 1">
              <div class="dialog-row">
                <el-col :span="3">
                  <span>会员价：</span>
                  <el-input-number
                    v-model="form.vipPrice"
                    :min="0"
                    :precision="2"
                    :disabled="true"
                    :controls="false"
                  />
                </el-col>
                <div class="dialog-inputs">
                  <el-form-item label='首付比例：' :prop="form.installmentSupport === 1?'vipDownPaymentRate':''">
                    <el-input-number
                      v-model="form.vipDownPaymentRate"
                      :min="0"
                      :controls="false"
                    />
                  </el-form-item>
                  <div class="dialog-units">%</div>
                  <div class="dialog-text">（首付金额：{{ form.memberSfPirce ||0 }}，尾款金额：{{ form.memberWfPirce ||0 }}）</div>
                </div>
              </div>
              <div class="dialog-row">
                <el-col :span="3">
                  <span>活动价：</span>
                  <el-input-number
                    v-model="form.actPrice"
                    :min="0"
                    :precision="2"
                    :disabled="true"
                    :controls="false"
                  />
                </el-col>
                <div class="dialog-inputs">
                  <el-form-item label='首付比例：' :prop="form.installmentSupport === 1?'actDownPaymentRate':''">
                    <el-input-number
                      v-model="form.actDownPaymentRate"
                      :min="0"
                      :controls="false"
                    />
                  </el-form-item>
                  <div class="dialog-units">%</div>
                  <div class="dialog-text">（首付金额：{{ form.activeSfPirce ||0 }}，尾款金额：{{ form.activeWfPirce ||0 }}）</div>
                </div>
              </div>
            </template>
          </el-form-item>

          <el-form-item v-if="form.installmentSupport === 1" class="select-demo" label='课程协议' :prop="form.installmentSupport === 1?'agreementId':''">
            <el-select
              v-model="form.agreementId"
              placeholder="请选择"
            >
              <el-option
                v-for="item in insSupport"
                :key="item.id"
                style="width: 100%;"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.installmentSupport === 1" label='分期支付类型' :prop="form.installmentSupport === 1?'installmentTypes':''">
            <el-checkbox-group v-model="form.installmentTypes">
              <el-checkbox label="HAI_ER">
                学易分期
                <span v-if="form.installmentTypes.includes('HAI_ER')" class="record-pop" @click.stop="onRecordPop('HAI_ER')">利息配置</span>
              </el-checkbox>
              <el-checkbox label="HAIER_FINANCE">
                海尔消费金融
                <span v-if="form.installmentTypes.includes('HAIER_FINANCE')" class="record-pop" @click.stop="onRecordPop('HAIER_FINANCE')">利息配置</span>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label='开卡样式' prop='myselfPicFile.fileUrl'>
            <el-radio-group v-model="form.openCardStyle">
              <el-radio :label="1">默认：动态显示会员可省多少元</el-radio>
              <el-radio :label="2">自定义</el-radio>
            </el-radio-group>
            <template v-if="form.openCardStyle !== 2">
              <upload-file
                class="cardStyle"
                :max-limit="1"
                :file-list='openCard'
                @remove="openCardRemoveImg"
                @success="openCardUploadSuccess"
              />
              <div style="margin-top: 15px;">
                <el-input v-model="form.myselfDefiLink" placeholder="请输入内容" class="input-with-select">
                  <el-select slot="prepend" v-model="form.linkType">
                    <el-option label="内部链接" :value="1" />
                    <el-option label="外部链接" :value="2" />
                  </el-select>
                </el-input>
              </div>
            </template>
          </el-form-item>

          <el-form-item label='二维码配置' prop="qrPicFile.fileUrl">
            <upload-file
              :max-limit="1"
              :file-list='qrCode'
              @remove="handleQrCodeRemoveImg"
              @success="qrCodeUploadSuccess"
            />
          </el-form-item>

          <el-form-item label='展示顺序' prop='sort'>
            <el-input-number
              v-model="form.sort"
              :min="0"
              :max="1000000000000"
              :precision="0"
              class="yz-input-number"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label='是否上架' prop='shelfStatus'>
            <el-radio-group v-model="form.shelfStatus" @change="handlePutawayChange">
              <el-radio :label="0" :disabled="isEdit">待上架</el-radio>
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="2">下架</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.shelfStatus===1" label='上架时间' prop='onShelfTime'>
            <el-date-picker
              v-model="form.onShelfTime"
              value-format="yyyy-MM-dd HH:mm"
              type="datetime"
              placeholder="选择日期"
            />
          </el-form-item>

          <el-form-item label='是否推荐' prop='recommendStatus'>
            <el-radio-group v-model="form.recommendStatus">
              <el-radio :label="1">推荐</el-radio>
              <el-radio :label="2">不推荐</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label='列表图片' prop='fileUrl'>
            <el-upload
              :class="{hide:hideUpload}"
              class="avatar-uploader"
              list-type="picture-card"
              action="/file/webuploader.do"
              :on-preview="handlePictureCardPreview"
              :on-success="uploadSuccess"
              :on-remove="handleRemoveImg"
              :on-exceed="handleExceedMax"
              :file-list="fileList"
              :limit="1"
            >
              <i class="el-icon-plus" />
            </el-upload>
            <el-dialog :visible.sync="dialogUploadVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </el-form-item>

          <el-form-item label='列表页简介' prop='goodsShelfBaseIntroduce'>
            <el-input
              v-model="form.goodsShelfBaseIntroduce"
              rows="3"
              type="textarea"
              placeholder="请输入"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <select-goods :visible.sync="sgVisible" :list="tableData" :type="form.goodsShelfType" @updateTable="updateTable" />
    </common-dialog>

    <el-dialog
      title="利息配置"
      :visible.sync="showRecordPop"
      width="50%"
      :before-close="handleClose"
    >
      <el-form label-width="150px">
        <el-form-item label="支付类型:" prop="type">
          <span>{{ typeText }}</span>
        </el-form-item>

        <el-form-item label="配置分期参数:">
          <el-table :data="ruleForm" border style="width: 100%">

            <el-table-column label="支持期数" width="180">
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.isEnable">{{ scope.row.period + '期' }}</el-checkbox>
              </template>
            </el-table-column>

            <el-table-column label="配置">
              <template slot-scope="scope">
                <el-form-item label="利息:" :rules="{ required: true, }">
                  <span>{{ scope.row.interestRate }}%</span>
                </el-form-item>

                <el-form-item label="利息模式:" :rules="{ required: true }">
                  <el-radio-group
                    v-model="scope.row.subsidyType"
                    :disabled="!Boolean(scope.row.isEnable)"
                    @input="(ev) => handleRadio(scope.$index, ev, scope.row)"
                  >
                    <el-radio
                      v-for="(item, index) in scope.row.subsidyList"
                      :key="index"
                      :label="item.label"
                      :disabled="item.text === '混合'"
                    >
                      {{ item.text }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item
                  label="商贴比例:"
                  :rules="{ required: true, message: '请输入', trigger: 'blur' }"
                >
                  <el-input-number
                    v-model="scope.row.subsidyProportion"
                    :controls="false"
                    :precision="2"
                    :min="0"
                    :max="100"
                    placeholder="请输入"
                    :disabled="true"
                  />
                  <span style="padding-left: 10px">%</span>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import selectGoods from './select-goods';
import { splitChar } from '@/utils';
import { ossUri } from '@/config/request';
import { getMulPrecision } from '@/utils/precision';

export default {
  components: {
    selectGoods
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: null
    },
    goodsId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const validateSort = (rule, value, callback) => {
      for (let i = 0; i < this.tableData.length; i++) {
        const item = this.tableData[i];
        if (item.sort === null || item.sort === '' || item.sort === undefined) {
          callback(new Error('请输入商品的排序号'));
        }
      }
      callback();
    };
    const validateOpenCard = (rule, value, callback) => {
      if (this.goodsId) callback();
      if (this.form.openCardStyle === 2) {
        if (!this.form.myselfPicFile.fileUrl) {
          callback(new Error('请上传图片'));
        } else if (!this.form.myselfDefiLink) {
          callback(new Error('请输入地址'));
        } else {
          callback();
        }
      }
      callback();
    };
    return {
      typeText: '',
      ruleForm: [],
      recordType: '',
      showRecordPop: false,

      dialogImageUrl: '',
      dialogUploadVisible: false,
      hideUpload: false,
      loading: false,
      sgVisible: false,
      originDetails: null,
      fileList: [],
      tableData: [],
      insSupport: [],
      qrCode: [],
      openCard: [],
      form: {
        goodsShelfType: '1',
        goodsShelfName: '',
        recommendStatus: 2,
        stock: undefined,
        sort: undefined,
        refundSupport: 1,
        installmentSupport: 0,
        agreementId: '',
        memberSfPirce: '',
        memberWfPirce: '',
        vipDownPaymentRate: 10,
        activeSfPirce: '',
        activeWfPirce: '',
        actDownPaymentRate: 10,
        purchaseBaseNumber: null,
        shelfStatus: 0,
        vipPrice: undefined,
        marketPrice: undefined,
        actPrice: undefined,
        // empPrice: undefined,
        onShelfTime: null,
        purchaseRule: [],
        refundRule: [],
        installmentTypes: [],
        fileUrl: '',
        goodsShelfPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 二维码
        qrPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 开卡样式
        myselfPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        myselfDefiLink: '', // 自定义内容

        linkType: 1, // 1 内部 2 外部
        openCardStyle: 1, // 选择定义类型
        goodsShelfBaseIntroduce: '',
        goodsShelfDetails: []
      },
      rules: {
        commodity: [{ validator: validateSort, trigger: 'blur' }],
        'myselfPicFile.fileUrl': [{ validator: validateOpenCard, trigger: 'blur' }],
        goodsShelfType: [{ required: true, message: '请选择', trigger: 'change' }],
        goodsShelfName: [{ required: true, message: '请输入', trigger: 'blur' }],
        stock: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        refundSupport: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        agreementId: [{ required: true, message: '请选择', trigger: 'change' }],
        installmentSupport: [{ required: true, message: '请选择', trigger: 'change' }],
        vipDownPaymentRate: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value === '' || value === undefined) {
                callback(new Error('请输入'));
              } else if (value >= 10 && value < 100) {
                this.form.memberSfPirce = Number(this.form.vipPrice * value / 100).toFixed(2);
                this.form.memberWfPirce = Number(this.form.vipPrice - this.form.memberSfPirce).toFixed(2);
                if (this.form.memberWfPirce < 2000) {
                  callback(new Error('尾款金额必须≥2000，请重新设置首付比例！'));
                } else callback();
              } else {
                callback(new Error('首付比例必须满足：10%≤首付比例＜100%'));
              }
            }
          }
        ],
        actDownPaymentRate: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value === '' || value === undefined) {
                callback(new Error('请输入'));
              } else if (value >= 10 && value < 100) {
                this.form.activeSfPirce = Number(this.form.actPrice * value / 100).toFixed(2);
                this.form.activeWfPirce = Number(this.form.actPrice - this.form.activeSfPirce).toFixed(2);
                if (this.form.activeWfPirce < 2000) {
                  callback(new Error('尾款金额必须≥2000，请重新设置首付比例！'));
                } else callback();
              } else {
                callback(new Error('首付比例必须满足：10%≤首付比例＜100%'));
              }
            }
          }
        ],
        shelfStatus: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        vipPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        marketPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        actPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        // empPrice: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        onShelfTime: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        goodsShelfBaseIntroduce: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        fileUrl: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        refundRule: [{ required: true, message: '请选择', trigger: 'change' }],
        installmentTypes: [{ required: true, message: '请选择', trigger: 'change' }]
      }

    };
  },
  computed: {
    isEdit() {
      if (this.originDetails && this.goodsId) {
        if (this.originDetails.shelfStatus !== 0 && this.goodsId) {
          return true;
        } else {
          return false;
        }
      }
      return false;
    }
  },
  methods: {
    init() {
      this.loading = true;
      this.$post('getAgreementList', { }, { json: 'application/json' }).then(res => {
        if (res.code === '00') {
          this.insSupport = res?.body || [];
          if (this.goodsId) {
            this.getGoodsInfo();
          } else this.loading = false;
        } else this.loading = false;
      });
    },
    // 切换类型时候清空关联商品
    clearCommodity() {
      this.tableData = [];
    },
    // 会员价变化后，联动其他数值变动
    computeVipPrice(event) {
      const vas = event.target.value;
      if (vas) {
        this.form.memberSfPirce = Number(vas * this.form.vipDownPaymentRate / 100).toFixed(2);
        this.form.memberWfPirce = Number(vas - this.form.memberSfPirce).toFixed(2);
      } else {
        this.form.memberSfPirce = 0;
        this.form.memberWfPirce = 0;
      }
    },
    // 活动价变化后，联动其他数值变动
    computeActPrice(event) {
      const vas = event.target.value;
      if (vas) {
        this.form.activeSfPirce = Number(vas * this.form.actDownPaymentRate / 100).toFixed(2);
        this.form.activeWfPirce = Number(vas - this.form.activeSfPirce).toFixed(2);
      } else {
        this.form.activeSfPirce = 0;
        this.form.activeWfPirce = 0;
      }
    },
    handlePutawayChange() {
      this.form.onShelfTime = null;
    },
    handleValidateRule(value) {
      if (value === 1) {
        this.$set(this.rules, 'refundRule', [{ required: true, message: '请选择', trigger: 'change' }]);
      } else {
        this.$delete(this.rules, 'refundRule');
      }
    },
    instsupportRule(value) {
      if (value === 1) {
        this.form.memberSfPirce = Number(this.form.vipPrice * this.form.vipDownPaymentRate / 100).toFixed(2);
        this.form.memberWfPirce = Number(this.form.vipPrice - this.form.memberSfPirce).toFixed(2);
        this.form.activeSfPirce = Number(this.form.actPrice * this.form.actDownPaymentRate / 100).toFixed(2);
        this.form.activeWfPirce = Number(this.form.actPrice - this.form.activeSfPirce).toFixed(2);
      } else {
        this.form.memberSfPirce = 0;
        this.form.memberWfPirce = 0;
        this.form.activeSfPirce = 0;
        this.form.activeWfPirce = 0;
      }
    },
    deleteGoods(row, index) {
      this.tableData.splice(index, 1);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 获取编辑表单默认详情数据
    async getGoodsInfo() {
      const data = {
        goodsShelfId: this.goodsId
      };
      const { fail, body } = await this.$post('getPutCommodityInfo', data);
      if (!fail) {
        this.originDetails = body;
        this.form.goodsShelfType = body.goodsShelfType;
        this.form.goodsShelfName = body.goodsShelfName;
        // 获取详情时，重新计算首付价格等数值
        this.form.installmentSupport = body.installmentSupport || 0;
        this.form.agreementId = body.agreementId;
        this.form.vipPrice = body.vipPrice || 0;
        this.form.actPrice = body.actPrice || 0;
        this.form.vipDownPaymentRate = body.vipDownPaymentRate || 10;
        this.form.actDownPaymentRate = body.actDownPaymentRate || 10;
        if (body.installmentSupport === 1) {
          this.form.memberSfPirce = Number(this.form.vipPrice * this.form.vipDownPaymentRate / 100).toFixed(2);
          this.form.memberWfPirce = Number(this.form.vipPrice - this.form.memberSfPirce).toFixed(2);
          this.form.activeSfPirce = Number(this.form.actPrice * this.form.actDownPaymentRate / 100).toFixed(2);
          this.form.activeWfPirce = Number(this.form.actPrice - this.form.activeSfPirce).toFixed(2);
        } else {
          this.form.memberSfPirce = body.memberSfPirce || '';
          this.form.memberWfPirce = body.memberWfPirce || '';
          this.form.activeSfPirce = body.activeSfPirce || '';
          this.form.activeWfPirce = body.activeWfPirce || '';
        }
        this.form.recommendStatus = body.recommendStatus;
        this.form.stock = body.stock;
        this.form.sort = body.sort;
        this.form.refundSupport = body.refundSupport;
        this.form.purchaseBaseNumber = body.purchaseBaseNumber;
        this.form.shelfStatus = body.shelfStatus;
        this.form.marketPrice = body.marketPrice;
        this.form.linkType = body.linkType;
        // this.form.empPrice = body.empPrice;
        this.form.onShelfTime = body.onShelfTime;
        this.form.purchaseRule = splitChar(body.purchaseRule);
        this.form.refundRule = splitChar(body.refundRule);
        this.form.fileUrl = body.goodsShelfPic;
        this.form.openCardStyle = body.openCardStyle;
        this.form.myselfDefiLink = body.myselfDefiLink;
        this.form.myselfPicFile.isAdd = 0;
        this.form.qrPicFile.isAdd = 0;
        this.form.installmentConfigs = body?.installmentConfigs || {};
        this.form.installmentTypes = body?.installmentTypes || [];

        this.form.qrPicFile.fileUrl = body.goodsShelfQrCode; // 二维码

        if (body.myselfDefiPic) {
          this.form.myselfPicFile.fileUrl = body.myselfDefiPic;
        }
        this.form.goodsShelfPicFile.fileUrl = body.goodsShelfPic;
        this.form.goodsShelfPicFile.isAdd = 0;// 回显时候isAdd为0
        this.form.goodsShelfBaseIntroduce = body.goodsShelfBaseIntroduce;
        // this.tableData = body.goodsShelfDetails;
        this.tableData = body.goodsShelfDetails.map(item => {
          return {
            goodsBaseId: Number(item.goodsBaseId),
            goodsName: item.goodsName,
            sort: item.sort
          };
        });
        this.fileList.push({ url: ossUri + body.goodsShelfPic });
        if (body.goodsShelfQrCode) {
          this.qrCode.push({ url: ossUri + body.goodsShelfQrCode });
        }
        if (body.myselfDefiPic) {
          this.openCard.push({ url: ossUri + body.myselfDefiPic });
        }
        this.hideUpload = true;
      }
      this.loading = false;
    },
    updateTable(list) {
      this.tableData = list;
    },
    // 打开 选择第几期 弹窗
    onRecordPop(typed) {
      console.log('第几期选择', typed);
      this.recordType = typed;
      this.form.installmentTypes.push(typed);
      this.typeText = typed === 'HAI_ER' ? '学易分期' : '海尔消费金融';
      const recordId = this.form.installmentConfigs[typed];
      this.$http
        .get('/puGoodsShelf/getInstallmentConfigByType.do', { params: { installmentType: typed }})
        .then((res) => {
          const { fail, body } = res;
          if (fail && !body.length) {
            this.$message.warning('没有分期配置数据');
            return;
          }
          this.ruleForm = [];
          const newData = this.uniqueArrayByKey(body, 'period');
          // 赋值：设置选中状态
          this.ruleForm = newData.map(item => {
            item.isEnable = false;
            item['subsidyList'] = [];
            body.forEach(chils => {
              if (chils.period === item.period) {
                item['subsidyList'].push({
                  id: chils.id,
                  interestRate: getMulPrecision(chils.interestRate, 100),
                  label: chils.subsidyType,
                  period: chils.period,
                  defaultStatus: chils.defaultStatus,
                  subsidyProportion: chils.subsidyProportion,
                  text: ['客息', '混合', '贴息'][chils.subsidyType - 1]
                });
                // 如果为空，则默认选中
                if (!recordId?.length) {
                  if (chils.defaultStatus) {
                    item.isEnable = true;
                    item.subsidyType = chils.subsidyType;
                    item['subsidyProportion'] = chils.subsidyProportion;
                  }
                } else if (recordId?.includes(chils.id)) {
                  item.isEnable = true;
                  item.subsidyType = chils.subsidyType;
                  item['subsidyProportion'] = chils.subsidyProportion;
                }
              }
            });
            return {
              period: item.period,
              isEnable: item.isEnable,
              subsidyList: item.subsidyList,
              interestRate: getMulPrecision(item.interestRate, 100),
              subsidyType: item.subsidyType,
              subsidyProportion: item.subsidyProportion
            };
          });
          console.log('******newData***********', this.ruleForm);
        }).finally(() => {
          this.showRecordPop = true;
        });
    },
    // 去重
    uniqueArrayByKey(arr, key) {
      const seen = new Set();
      return arr.filter(item => {
        const value = JSON.stringify(item[key]);
        if (seen.has(value)) {
          return false;
        } else {
          seen.add(value);
          return true;
        }
      });
    },
    // 选择第几期-radio
    handleRadio(ins, vas, row) {
      this.ruleForm.forEach((item, inx) => {
        if (inx === ins) {
          item.subsidyList.forEach((chids, chidsIndex) => {
            if (chidsIndex === (vas - 1)) {
              console.log('******chids***********', { ...chids });
              row.interestRate = chids.interestRate;
              row.subsidyProportion = chids.subsidyProportion;
            }
          });
        }
      });
    },
    // 关闭 选择第几期 弹窗
    handleClose() {
      console.log('关闭 选择第几期');
      this.typeText = '';
      this.ruleForm = [];
      this.recordType = '';
      this.showRecordPop = false;
    },
    // 确定 选择第几期
    handleOk() {
      const initIds = [];
      this.ruleForm.forEach((item) => {
        if (item.isEnable && item.subsidyList?.length) {
          item.subsidyList.forEach((chils) => {
            if (item.subsidyType === chils.label) {
              initIds.push(chils.id);
            }
          });
        }
      });
      if (initIds.length === 0) {
        this.$message.warning('请至少选择一个分期配置');
        return;
      }
      this.form.installmentConfigs[this.recordType] = initIds;
      console.log('确定 选择第几期-config', initIds, { ...this.form });
      this.showRecordPop = false;
    },
    // 编辑表单提交按钮
    submitBtn() {
      let apiKey = 'addPutcommodity';
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form));
          console.log('******formData提交***********', apiKey);
          delete formData.fileUrl;
          formData.goodsShelfDetails = this.tableData;
          formData.purchaseRule = formData.purchaseRule.join();
          formData.refundRule = formData.refundRule.join();
          // 判断分期配置是否已经选择
          const newsConfigs = {};
          formData.installmentTypes = Array.from(new Set(formData.installmentTypes));
          const { installmentTypes, installmentConfigs } = formData;
          if (installmentTypes?.length) {
            for (let i = 0; i < installmentTypes.length; i++) {
              const keys = installmentTypes[i];
              if (!installmentConfigs[keys]) {
                this.$message.warning(`请选择 ${keys === 'HAI_ER' ? '学易分期' : '海尔消费金融'} 利息配置`);
                return;
              }
              newsConfigs[keys] = installmentConfigs[keys];
            }
          }
          formData.installmentConfigs = newsConfigs;
          const data = { ...formData };
          if (this.goodsId) {
            apiKey = 'editPutcommodity';
            data.goodsShelfId = this.goodsId;
            // 如果图片没有改变
            if (this.form.goodsShelfPicFile.isAdd === 0) {
              data.goodsShelfPicFile.fileUrl = this.originDetails.goodsShelfPic;
            }
          }
          console.log('insta--data', data);
          // 接口
          this.$post(apiKey, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              console.log('******提交成功后的关闭***********');
              this.$emit('close');
              this.$parent.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    // 关闭编辑弹窗
    close() {
      this.$refs['form'].resetFields();
      this.tableData = [];
      this.originDetails = null;
      this.fileList = [];
      this.qrCode = [];
      this.openCard = [];
      this.hideUpload = false;
      Object.assign(this.$data.form, this.$options.data().form);
      // Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      console.log('******手动关闭***********');
      this.$emit('close');
    },
    // 开卡样式
    openCardRemoveImg() {
      this.form.myselfPicFile.fileUrl = '';
    },
    openCardUploadSuccess({ response, file, fileList }) {
      this.form.myselfPicFile.fileUrl = response;
      this.form.myselfPicFile.isAdd = 1;
    },
    // 二维码进群
    handleQrCodeRemoveImg() {
      this.form.qrPicFile.fileUrl = '';
    },
    qrCodeUploadSuccess({ response, file, fileList }) {
      this.form.qrPicFile.fileUrl = response;
      this.form.qrPicFile.isAdd = 1;
    },
    handleExceedMax() {
      this.$message.error('只能上传一张图片！');
    },
    handleRemoveImg(file, fileList) {
      this.form.fileUrl = '';
      this.form.goodsShelfPicFile.fileUrl = '';
      this.hideUpload = false;
    },
    uploadSuccess(response, file, fileList) {
      this.form.fileUrl = response;
      this.form.goodsShelfPicFile.fileUrl = response;
      this.form.goodsShelfPicFile.isAdd = 1;
      this.hideUpload = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.padding-20 {
  padding: 20px;
}

.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

.table {
  margin: 10px 0;
}

.cardStyle {
  ::v-deep .el-upload--picture-card {
    width: 300px;
    height: 30px;
    line-height: 30px;
  }
  ::v-deep .el-upload--picture-card i {
    font-size: 14px;
  }
  ::v-deep .el-upload-list--picture-card .el-upload-list__item{
    width: 300px;
    height: 30px;
  }
}
::v-deep .el-select .el-input {
  width: 130px;
}

::v-deep .select-demo .el-select .el-input {
  width: auto;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.dialog-row {
  margin-top: 20px;
  display: flex;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;
  }

  .is-error {
    margin-bottom: 24px;
  }

  .el-form-item__error {
    padding-top: 4px;
  }

  .dialog-inputs {
    display: flex;
    align-items: center;

    .dialog-units {
      margin: 0 8px 0 4px;
    }

    .dialog-text {
      font-size: 14px;
      color: rgba(127, 127, 127, 0.8);
    }
  }
}

.record-pop {
  color: #02A7F0;
  cursor: pointer;
  margin-left: 10px;
}
</style>
