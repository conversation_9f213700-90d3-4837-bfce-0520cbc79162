<template>
  <div class="yz-base-container">
    <!-- 搜索区域 -->
    <el-form
      ref="searchForm"
      :model="searchForm"
      class="yz-search-form"
      label-width="120px"
      size="mini"
      @submit.native.prevent="handleSearch"
    >
      <el-form-item label="真实姓名:" prop="realName">
        <el-input
          v-model="searchForm.realName"
          placeholder="请输入真实姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="远智编号:" prop="yzCode">
        <el-input
          v-model="searchForm.yzCode"
          placeholder="请输入远智编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机号码:" prop="phone">
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
      <el-form-item label="签到时间:" prop="dateRange">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          size="mini"
        />
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="handleReset" />
      </div>
    </el-form>

    <!-- 操作按钮区域 -->
    <div class="yz-operation-box">
      <el-button
        type="primary"
        size="mini"
        @click="smartRiceDialogVisible = true"
      >赚取智米配置</el-button>
      <el-button
        type="primary"
        size="mini"
        @click="ruleDialogVisible = true"
      >签到奖励配置</el-button>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      border
      size="small"
      class="table-container"
    >
      <el-table-column label="用户信息" align="center">
        <template slot-scope="scope">
          <div class="user-info">
            <div>远智编号: {{ scope.row.yzCode }}</div>
            <div>真实姓名: {{ scope.row.realName }}</div>
            <div>昵称: {{ scope.row.nickname }}</div>
            <div>手机号码: {{ scope.row.mobile }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="signDate" label="签到时间" align="center" />
      <el-table-column prop="rewardZhimi" label="奖励智米数" align="center" />
      <el-table-column
        prop="cycleSignDays"
        label="周期内连签天数"
        align="center"
      />
    </el-table>

    <!-- 分页器 -->
    <div class="yz-table-pagination">
      <pagination
        :total="page.total"
        :page.sync="page.current"
        :limit.sync="page.size"
        @pagination="handlePagination"
      />
    </div>

    <!-- 签到规则配置弹窗 -->
    <sign-rule-dialog :visible.sync="ruleDialogVisible" />

    <!-- 赚取智米配置弹窗 -->
    <smart-rice-dialog :visible.sync="smartRiceDialogVisible" />
  </div>
</template>

<script>
import SignRuleDialog from './components/sign-rule-dialog';
import SmartRiceDialog from './components/smart-rice-dialog';

export default {
  name: 'SignInManage',
  components: { SignRuleDialog, SmartRiceDialog },
  data() {
    return {
      // 搜索表单
      searchForm: {
        realName: '',
        yzCode: '',
        phone: '',
        dateRange: []
      },
      // 表格数据
      tableLoading: false,
      tableData: [],
      // 分页
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      // 签到规则配置弹窗
      ruleDialogVisible: false,
      // 赚取智米配置弹窗
      smartRiceDialogVisible: false
    };
  },
  created() {
    this.fetchTableData();
  },
  methods: {
    // 获取表格数据
    async fetchTableData() {
      try {
        this.tableLoading = true;
        const params = {
          pageNum: (this.page.current - 1) * this.page.size,
          pageSize: this.page.size,
          realName: this.searchForm.realName,
          yzCode: this.searchForm.yzCode,
          mobile: this.searchForm.phone,
          startTime:
            this.searchForm.dateRange && this.searchForm.dateRange[0]
              ? this.searchForm.dateRange[0]
              : '',
          endTime:
            this.searchForm.dateRange && this.searchForm.dateRange[1]
              ? this.searchForm.dateRange[1]
              : ''
        };

        const result = await this.$http.post(
          '/operate/sign/record/list',
          params,
          { json: true }
        );

        this.tableData = result.body.data;
        this.page.total = result.body.recordsTotal;
      } catch (error) {
        console.error(error);
        this.$message.error('获取数据失败');
      } finally {
        this.tableLoading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.page.current = 1;
      this.fetchTableData();
    },

    // 重置搜索表单
    handleReset() {
      this.$refs.searchForm.resetFields();
      this.page.current = 1;
      this.fetchTableData();
    },

    // 分页
    handlePagination(val) {
      this.page.current = val.page;
      this.page.size = val.limit;
      this.fetchTableData();
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-search-form {
  margin: 20px 0 30px;
}

.yz-operation-box {
  margin-bottom: 10px;
  text-align: right;
}

.user-info {
  text-align: left;
}
</style>
